import pandas as pd
import json
import ast


csv_file = 'E_syndic.csv'

try:
    df_raw = pd.read_csv(csv_file)
    print(f"Fichier CSV chargé avec succès: {csv_file}")    
    print("Aperçu des données brutes:")
    display(df_raw.head())
    
except FileNotFoundError:
    print(f"Erreur: Fichier {csv_file} non trouvé")
    print("Vérifiez le chemin du fichier et réessayez")
except Exception as e:
    print(f"Erreur lors du chargement: {str(e)}")

def parse_json_data(json_str):
        parsed = ast.literal_eval(json_str)
        print("Parsing réussi")
        return parsed
    
# Parsing des données
parsed_data = parse_json_data(df_raw['data'].iloc[0])

df_syndics = pd.DataFrame(parsed_data)
print(f"DataFrame créé avec {len(df_syndics)} syndicats")

# Affichage des colonnes disponibles
print("\nColonnes disponibles:")
print(df_syndics.columns.tolist())

# Affichage des données de base
print("\n Aperçu des données:")
display(df_syndics.head())

class Cleaning:
    def __init__(self, df):
        self.df = df.copy()

    def clean(self):
        # Supprimer colonnes inutiles
        self.df.drop(columns=['id', 'lieu', 'description'], inplace=True)
        # Renommer colonnes
        self.df.rename(columns={
            'libelle': 'Programme',
            'created_at': 'Date_de_creation',
            'pays': 'Pays',
            'ville' : 'Ville',
            'superficie' : 'Superficie',
            'proprietaire' : 'Proprietaire',
            'loataires' : 'Locataires',
            'charges' : 'Charges',
            'incidents' : 'Incidents',
            'evenements' : 'Evenements'
            
        }, inplace=True)
        return self.df
cleaner = Cleaning(df_syndics)
df_clean = cleaner.clean()
df_clean.head()


# Classe de nettoyage corrigée
class CleaningFixed:
    def __init__(self, df):
        self.df = df.copy()
    
    def clean(self):
        # Vérifier et supprimer les colonnes inutiles qui existent
        columns_to_drop = []
        potential_drops = ['id', 'lieu', 'description']
        
        for col in potential_drops:
            if col in self.df.columns:
                columns_to_drop.append(col)
        
        if columns_to_drop:
            self.df.drop(columns=columns_to_drop, inplace=True)
            print(f'Colonnes supprimées: {columns_to_drop}')
        
        # Renommer colonnes (seulement celles qui existent)
        rename_mapping = {
            'libelle': 'Programme',
            'created_at': 'Date_de_creation',
            'pays': 'Pays',
            'ville': 'Ville',
            'superficie': 'Superficie',
            'proprietaire': 'Proprietaires',
            'locataires': 'Locataires',
            'charges': 'Charges',
            'incidents': 'Incidents',
            'evenements': 'Evenements'
        }
        
        # Filtrer le mapping pour ne garder que les colonnes existantes
        existing_rename = {old: new for old, new in rename_mapping.items() if old in self.df.columns}
        
        if existing_rename:
            self.df.rename(columns=existing_rename, inplace=True)
            print(f'Colonnes renommées: {existing_rename}')
        
        print(f'Colonnes finales: {list(self.df.columns)}')
        return self.df

# Utilisation de la classe corrigée
cleaner_fixed = CleaningFixed(df_syndics)
df_clean = cleaner_fixed.clean()
df_clean.head()

def explode_column(df, col_name, parent_col='Programme'):
    """Éclate une colonne contenant des listes d'objets"""
    rows = []
    
    for _, row in df.iterrows():
        data_list = row.get(col_name)
        
        # Vérifier que c'est une liste non vide
        if isinstance(data_list, list) and len(data_list) > 0:
            for item in data_list:
                if isinstance(item, dict):
                    item_copy = item.copy()
                    item_copy[parent_col] = row[parent_col]
                    rows.append(item_copy)
    
    return pd.DataFrame(rows)


print("Création des DataFrames éclatés...")

df_proprietaires = explode_column(df_clean, 'Proprietaire')
if not df_proprietaires.empty:
    print("Colonnes propriétaires:", df_proprietaires.columns.tolist())
    display(df_proprietaires.head())
else:
    print("Aucun propriétaire trouvé")
    df_proprietaires = pd.DataFrame(columns=['Programme', 'name', 'prenoms', 'email', 'contact'])

# DataFrame des locataires
df_locataires = explode_column(df_clean, 'Locataires')
print(f"\nDataFrame locataires créé avec {len(df_locataires)} entrées")
if not df_locataires.empty:
    print("Colonnes locataires:", df_locataires.columns.tolist())
    display(df_locataires.head())
else:
    print("Aucun locataire trouvé")
    df_locataires = pd.DataFrame(columns=['Programme', 'name', 'prenoms', 'email', 'contact'])

# DataFrame des incidents
df_incidents = explode_column(df_clean, 'Incidents')
print(f"\nDataFrame incidents créé avec {len(df_incidents)} entrées")
if not df_incidents.empty:
    print("Colonnes incidents:", df_incidents.columns.tolist())
    display(df_incidents.head())
else:
    print("Aucun incident trouvé")
    df_incidents = pd.DataFrame(columns=['Programme', 'titre', 'description', 'status', 'date_creation'])

# DataFrame des événements
df_evenements = explode_column(df_clean, 'Evenements')
print(f"\nDataFrame événements créé avec {len(df_evenements)} entrées")
if not df_evenements.empty:
    print("Colonnes événements:", df_evenements.columns.tolist())
    display(df_evenements.head())
else:
    print("Aucun événement trouvé")
    df_evenements = pd.DataFrame(columns=['Programme', 'titre', 'description', 'date_debut', 'date_fin', 'auteur'])


def analyser_proprietaires(programme_nom, df_proprietaires):
    """Analyse et affiche les propriétaires d'un programme"""
    print(f"\n PROGRAMME: {programme_nom}")
    
    # Filtrer les propriétaires pour ce programme
    prog_proprietaires = df_proprietaires[df_proprietaires['Programme'] == programme_nom] if not df_proprietaires.empty else pd.DataFrame()
    
    # Créer un DataFrame vide avec les colonnes attendues si pas de données
    if prog_proprietaires.empty:
        prog_proprietaires = pd.DataFrame(columns=['name', 'prenoms', 'email', 'contact'])
    
    print(f" Nombre de propriétaires: {len(prog_proprietaires)}")
    print(" Liste des propriétaires:")
    display(prog_proprietaires[['name', 'prenoms', 'email', 'contact']].reset_index(drop=True))
    

def analyser_locataires(programme_nom, df_locataires):
    """Analyse et affiche les locataires d'un programme"""
    print(f"\n PROGRAMME: {programme_nom}")
    
    # Filtrer les locataires pour ce programme
    prog_locataires = df_locataires[df_locataires['Programme'] == programme_nom] if not df_locataires.empty else pd.DataFrame()
    
    # Créer un DataFrame vide avec les colonnes attendues si pas de données
    if prog_locataires.empty:
        prog_locataires = pd.DataFrame(columns=['name', 'prenoms', 'email', 'contact'])
    
    print(f" Nombre de locataires: {len(prog_locataires)}")
    print(" Liste des locataires:")
    display(prog_locataires[['name', 'prenoms', 'email', 'contact']].reset_index(drop=True))


def analyser_charges(programme_nom, charges_data):
    """Analyse et affiche les charges d'un programme"""
    print(f"\n PROGRAMME: {programme_nom}")
    
    if isinstance(charges_data, dict) and charges_data:
        # Appels de fonds
        appel_fonds = charges_data.get('appel_fonds', {})
        imprevus = charges_data.get('imprevus', {})
        
        # Créer un DataFrame pour ce programme
        charge_data = {
            'Type de charge': ['Charges régulières', 'Charges payées', 'Charges impayées', 
                              'Charges exceptionnelles', 'Exceptionnelles payées', 'Exceptionnelles impayées'],
            'Montant (XOF)': [
                f"{appel_fonds.get('total_charge', 0):,.0f}",
                f"{appel_fonds.get('total_payer', 0):,.0f}",
                f"{appel_fonds.get('total_impayes', 0):,.0f}",
                f"{imprevus.get('total_charge_exceptionnel', 0):,.0f}",
                f"{imprevus.get('total_charge_exceptionnel_payer', 0):,.0f}",
                f"{imprevus.get('total_charge_exceptionnel_impayer', 0):,.0f}"
            ]
        }
        
        charges_df = pd.DataFrame(charge_data)
        print(" Détail des charges:")
        display(charges_df)
        
        # Calcul du taux de paiement
        #total_charge = appel_fonds.get('total_charge', 0)
        #total_paye = appel_fonds.get('total_payer', 0)
        #if total_charge > 0:
            #taux_paiement = (total_paye / total_charge) * 100
            #print(f" Taux de paiement: {taux_paiement:.1f}%")
    else:
        # Afficher un DataFrame vide avec les en-têtes
        charge_data_vide = {
            'Type de charge': ['Charges régulières', 'Charges payées', 'Charges impayées', 
                              'Charges exceptionnelles', 'Exceptionnelles payées', 'Exceptionnelles impayées'],
            'Montant (XOF)': ['0', '0', '0', '0', '0', '0']
        }
        charges_df_vide = pd.DataFrame(charge_data_vide)
        print(" Détail des charges:")
        display(charges_df_vide)

def extraire_charges_proprietaires(proprietaires):
    """Extrait les données détaillées des charges des propriétaires"""
    charges_data = []
    
    for proprio in proprietaires:
        if 'charges' in proprio and proprio['charges']:
            charges = proprio['charges']
            
            # Données de base du propriétaire
            base_data = {
                'proprietaire_id': proprio['id'],
                'nom': proprio['name'],
                'prenoms': proprio['prenoms'],
                'email': proprio['email'],
                'contact': proprio['contact'],
                'total_charge': charges.get('total_charge', 0),
                'total_payer': charges.get('total_payer', 0),
                'total_impayes': charges.get('total_impayes', 0),
                'total_charge_exceptionnel': charges.get('total_charge_exceptionnel', 0),
                'total_charge_exceptionnel_payer': charges.get('total_charge_exceptionnel_payer', 0),
                'total_charge_exceptionnel_impayer': charges.get('total_charge_exceptionnel_impayer', 0),
                'nb_appels_fonds': len(charges.get('list_appelfond', [])),
                'nb_appels_fonds_payes': len(charges.get('list_appelfond_payer', [])),
                'nb_appels_fonds_impayes': len(charges.get('list_appelfond_impayes', [])),
                'nb_imprevus': len(charges.get('list_imprevus', [])),
                'nb_imprevus_payes': len(charges.get('list_imprevus_payer', [])),
                'nb_imprevus_impayes': len(charges.get('list_imprevus_impayes', []))
            }
            
            charges_data.append(base_data)
    
    return pd.DataFrame(charges_data)

def extraire_appels_fonds_detailles(proprietaires):
    """Extrait le détail de tous les appels de fonds"""
    appels_fonds_data = []
    
    for proprio in proprietaires:
        if 'charges' in proprio and proprio['charges']:
            charges = proprio['charges']
            
            # Traitement des appels de fonds
            for appel in charges.get('list_appelfond', []):
                appel_data = {
                    'proprietaire_id': proprio['id'],
                    'nom_proprietaire': f"{proprio['name']} {proprio['prenoms']}",
                    'appel_id': appel['id'],
                    'appelfond_id': appel['appelfond_id'],
                    'libelle_appel': appel['appelfond']['libelle'],
                    'lot': appel['lot']['lot'],
                    'ilot': appel['lot']['ilot'],
                    'solde': float(appel['solde']),
                    'status': appel['status'],
                    'date_fin': appel['datefin'],
                    'type_charge': 'Appel de fonds'
                }
                appels_fonds_data.append(appel_data)
            
            # Traitement des imprévus
            for imprevu in charges.get('list_imprevus', []):
                imprevu_data = {
                    'proprietaire_id': proprio['id'],
                    'nom_proprietaire': f"{proprio['name']} {proprio['prenoms']}",
                    'appel_id': imprevu['id'],
                    'appelfond_id': imprevu['imprevu_id'],
                    'libelle_appel': imprevu['imprevus']['titre'],
                    'lot': imprevu['lot']['lot'],
                    'ilot': imprevu['lot']['ilot'],
                    'solde': float(imprevu['montant']),
                    'status': imprevu['status'],
                    'date_fin': imprevu['datefin'],
                    'type_charge': 'Charge exceptionnelle'
                }
                appels_fonds_data.append(imprevu_data)
    
    return pd.DataFrame(appels_fonds_data)

def analyser_incidents(programme_nom, df_incidents):
    """Analyse et affiche les incidents d'un programme"""
    print(f"\n PROGRAMME: {programme_nom}")
    
    # Filtrer les incidents pour ce programme
    prog_incidents = df_incidents[df_incidents['Programme'] == programme_nom] if not df_incidents.empty else pd.DataFrame()
    
    # Créer un DataFrame vide avec les colonnes attendues si pas de données
    if prog_incidents.empty:
        prog_incidents = pd.DataFrame(columns=['reference', 'date', 'auteur', 'status', 'priorite', 'secteur'])
    
    print(f" Nombre d'incidents: {len(prog_incidents)}")
    print(" Liste des incidents:")
    display(prog_incidents[['reference', 'date', 'auteur', 'status', 'priorite', 'secteur']].reset_index(drop=True))
    
    #if not prog_incidents.empty:
        # Statistiques pour ce programme
        #print("\n Répartition par statut:")
        #status_count = prog_incidents['status'].value_counts().reset_index()
        #status_count.columns = ['Statut', 'Nombre']
        #display(status_count)
        
        #print("\n⚡ Répartition par priorité:")
        #priorite_count = prog_incidents['priorite'].value_counts().reset_index()
        #priorite_count.columns = ['Priorité', 'Nombre']
        #display(priorite_count)
    #else:
        #print(" Ce programme pourra accueillir des incidents dans le futur")

def analyser_evenements(programme_nom, df_evenements):
    """Analyse et affiche les événements d'un programme"""
    print(f"\n PROGRAMME: {programme_nom}")
    
    # Filtrer les événements pour ce programme
    prog_evenements = df_evenements[df_evenements['Programme'] == programme_nom] if not df_evenements.empty else pd.DataFrame()
    
    # Créer un DataFrame vide avec les colonnes attendues si pas de données
    if prog_evenements.empty:
        prog_evenements = pd.DataFrame(columns=['titre', 'auteur', 'status', 'date_debut', 'heure_debut', 'bruyant'])
    
    print(f" Nombre d'événements: {len(prog_evenements)}")
    print(" Liste des événements:")
    display(prog_evenements[['titre', 'auteur', 'status', 'date_debut', 'heure_debut', 'bruyant']].reset_index(drop=True))
    
    #if not prog_evenements.empty:
        # Statistiques pour ce programme
        #print("\n Répartition par statut:")
        #status_events = prog_evenements['status'].value_counts().reset_index()
        #status_events.columns = ['Statut', 'Nombre']
        #display(status_events)
        
        #print("\n Événements bruyants:")
        #bruyant_count = prog_evenements['bruyant'].value_counts().reset_index()
        #bruyant_count.columns = ['Bruyant', 'Nombre']
        #display(bruyant_count)
    #else:
        #print(" Ce programme pourra accueillir des événements dans le futur")

class ESyndicMetrics:
    """
    Classe réutilisable pour calculer toutes les métriques E-Syndic
    """
    
    def __init__(self, df_clean, df_proprietaires, df_locataires, df_incidents, df_evenements):
        self.df_clean = df_clean
        self.df_proprietaires = df_proprietaires
        self.df_locataires = df_locataires
        self.df_incidents = df_incidents
        self.df_evenements = df_evenements
    
    def get_total_proprietaires_locataires_par_programme(self):
        """
        Retourne le nombre total de propriétaires et locataires par programme
        """
        results = []
        
        for _, row in self.df_clean.iterrows():
            programme = row['Programme']
            
            # Compter propriétaires
            nb_proprietaires = len(self.df_proprietaires[self.df_proprietaires['Programme'] == programme]) if not self.df_proprietaires.empty else 0
            
            # Compter locataires
            nb_locataires = len(self.df_locataires[self.df_locataires['Programme'] == programme]) if not self.df_locataires.empty else 0
            
            results.append({
                'Programme': programme,
                'Nb_Proprietaires': nb_proprietaires,
                'Nb_Locataires': nb_locataires,
                'Total_Personnes': nb_proprietaires + nb_locataires
            })
        
        return pd.DataFrame(results)
    
    def get_nombre_biens_par_programme(self):
        """
        Retourne le nombre de biens par programme (basé sur le nombre de propriétaires + locataires)
        """
        results = []
        
        for _, row in self.df_clean.iterrows():
            programme = row['Programme']
            
            # Estimer le nombre de biens (propriétaires + locataires uniques)
            nb_proprietaires = len(self.df_proprietaires[self.df_proprietaires['Programme'] == programme]) if not self.df_proprietaires.empty else 0
            nb_locataires = len(self.df_locataires[self.df_locataires['Programme'] == programme]) if not self.df_locataires.empty else 0
            
            # Approximation: 1 bien peut avoir 1 propriétaire et potentiellement 1 locataire
            nb_biens_estimes = max(nb_proprietaires, nb_locataires) if nb_proprietaires > 0 or nb_locataires > 0 else 0
            
            results.append({
                'Programme': programme,
                'Nb_Biens_Estimes': nb_biens_estimes,
                'Nb_Proprietaires': nb_proprietaires,
                'Nb_Locataires': nb_locataires
            })
        
        return pd.DataFrame(results)
    
    def get_biens_en_location_par_proprietaire(self):
        """
        Retourne le nombre de biens en location par propriétaire
        """
        if self.df_proprietaires.empty or self.df_locataires.empty:
            return pd.DataFrame(columns=['Programme', 'Proprietaire', 'Nb_Biens_Loues'])
        
        results = []
        
        # Grouper par programme
        for programme in self.df_proprietaires['Programme'].unique():
            proprietaires_prog = self.df_proprietaires[self.df_proprietaires['Programme'] == programme]
            locataires_prog = self.df_locataires[self.df_locataires['Programme'] == programme]
            
            # Pour chaque propriétaire, estimer le nombre de biens loués
            for _, prop in proprietaires_prog.iterrows():
                proprietaire_nom = f"{prop.get('name', '')} {prop.get('prenoms', '')}".strip()
                
                # Estimation simple: ratio locataires/propriétaires dans le programme
                nb_locataires_prog = len(locataires_prog)
                nb_proprietaires_prog = len(proprietaires_prog)
                
                if nb_proprietaires_prog > 0:
                    biens_loues_estimes = round(nb_locataires_prog / nb_proprietaires_prog, 1)
                else:
                    biens_loues_estimes = 0
                
                results.append({
                    'Programme': programme,
                    'Proprietaire': proprietaire_nom,
                    'Nb_Biens_Loues': biens_loues_estimes
                })
        
        return pd.DataFrame(results)
    
    def get_evenements_par_programme_et_periode(self):
        """
        Retourne le nombre total d'événements par programme et par période (année)
        """
        if self.df_evenements.empty:
            return pd.DataFrame(columns=['Programme', 'Annee', 'Nb_Evenements'])
        
        results = []
        
        # Extraire l'année de la date de début si disponible
        df_events_copy = self.df_evenements.copy()
        
        # Essayer d'extraire l'année de date_debut
        if 'date_debut' in df_events_copy.columns:
            df_events_copy['annee'] = pd.to_datetime(df_events_copy['date_debut'], errors='coerce').dt.year
        else:
            df_events_copy['annee'] = 'Non définie'
        
        # Grouper par programme et année
        grouped = df_events_copy.groupby(['Programme', 'annee']).size().reset_index(name='Nb_Evenements')
        
        for _, row in grouped.iterrows():
            results.append({
                'Programme': row['Programme'],
                'Annee': row['annee'],
                'Nb_Evenements': row['Nb_Evenements']
            })
        
        return pd.DataFrame(results)
    
    def get_evenements_par_organisateur(self):
        """
        Retourne le nombre d'événements organisés par les propriétaires et locataires
        """
        if self.df_evenements.empty:
            return pd.DataFrame(columns=['Programme', 'Type_Organisateur', 'Nb_Evenements'])
        
        results = []
        
        for programme in self.df_evenements['Programme'].unique():
            events_prog = self.df_evenements[self.df_evenements['Programme'] == programme]
            
            # Récupérer les noms des propriétaires et locataires pour ce programme
            proprietaires_prog = self.df_proprietaires[self.df_proprietaires['Programme'] == programme] if not self.df_proprietaires.empty else pd.DataFrame()
            locataires_prog = self.df_locataires[self.df_locataires['Programme'] == programme] if not self.df_locataires.empty else pd.DataFrame()
            
            # Créer des listes de noms complets
            noms_proprietaires = []
            if not proprietaires_prog.empty:
                noms_proprietaires = [f"{row.get('name', '')} {row.get('prenoms', '')}".strip().upper() 
                                    for _, row in proprietaires_prog.iterrows()]
            
            noms_locataires = []
            if not locataires_prog.empty:
                noms_locataires = [f"{row.get('name', '')} {row.get('prenoms', '')}".strip().upper() 
                                 for _, row in locataires_prog.iterrows()]
            
            # Compter les événements par type d'organisateur
            nb_events_proprietaires = 0
            nb_events_locataires = 0
            nb_events_autres = 0
            
            for _, event in events_prog.iterrows():
                auteur = str(event.get('auteur', '')).strip().upper()
                
                if any(nom in auteur for nom in noms_proprietaires if nom):
                    nb_events_proprietaires += 1
                elif any(nom in auteur for nom in noms_locataires if nom):
                    nb_events_locataires += 1
                else:
                    nb_events_autres += 1
            
            # Ajouter les résultats
            results.extend([
                {'Programme': programme, 'Type_Organisateur': 'Propriétaires', 'Nb_Evenements': nb_events_proprietaires},
                {'Programme': programme, 'Type_Organisateur': 'Locataires', 'Nb_Evenements': nb_events_locataires},
                {'Programme': programme, 'Type_Organisateur': 'Autres', 'Nb_Evenements': nb_events_autres}
            ])
        
        return pd.DataFrame(results)
    
    def get_incidents_par_programme(self):
        """
        Retourne le nombre d'incidents par programme
        """
        if self.df_incidents.empty:
            return pd.DataFrame(columns=['Programme', 'Nb_Incidents', 'Incidents_Resolus', 'Incidents_En_Cours'])
        
        results = []
        
        # Grouper par programme
        for programme in self.df_incidents['Programme'].unique():
            incidents_prog = self.df_incidents[self.df_incidents['Programme'] == programme]
            
            nb_total = len(incidents_prog)
            
            # Compter par statut si la colonne existe
            if 'status' in incidents_prog.columns:
                nb_resolus = len(incidents_prog[incidents_prog['status'].str.contains('résolu|fermé|terminé', case=False, na=False)])
                nb_en_cours = nb_total - nb_resolus
            else:
                nb_resolus = 0
                nb_en_cours = nb_total
            
            results.append({
                'Programme': programme,
                'Nb_Incidents': nb_total,
                'Incidents_Resolus': nb_resolus,
                'Incidents_En_Cours': nb_en_cours
            })
        
        return pd.DataFrame(results)
    
    def generer_rapport_complet(self):
        """
        Génère un rapport complet avec toutes les métriques
        """
        print("RAPPORT COMPLET E-SYNDIC")
        print("="*50)
        
        # 1. Propriétaires et locataires par programme
        print("\n1. PROPRIÉTAIRES ET LOCATAIRES PAR PROGRAMME")
        df_prop_loc = self.get_total_proprietaires_locataires_par_programme()
        display(df_prop_loc)
        
        # 2. Nombre de biens par programme
        print("\n2. NOMBRE DE BIENS PAR PROGRAMME")
        df_biens = self.get_nombre_biens_par_programme()
        display(df_biens)
        
        # 3. Biens en location par propriétaire
        print("\n3. BIENS EN LOCATION PAR PROPRIÉTAIRE")
        df_location = self.get_biens_en_location_par_proprietaire()
        if not df_location.empty:
            display(df_location)
        else:
            # Afficher un DataFrame vide avec les colonnes
            df_vide = pd.DataFrame(columns=['Programme', 'Proprietaire', 'Nb_Biens_Loues'])
            display(df_vide)
        
        # 4. Événements par programme et période
        print("\n4. ÉVÉNEMENTS PAR PROGRAMME ET PÉRIODE")
        df_events_periode = self.get_evenements_par_programme_et_periode()
        if not df_events_periode.empty:
            display(df_events_periode)
        else:
            # Afficher un DataFrame vide avec les colonnes
            df_vide = pd.DataFrame(columns=['Programme', 'Annee', 'Nb_Evenements'])
            display(df_vide)
        
        # 5. Événements par organisateur
        print("\n5. ÉVÉNEMENTS PAR TYPE D'ORGANISATEUR")
        df_events_org = self.get_evenements_par_organisateur()
        if not df_events_org.empty:
            display(df_events_org)
        else:
            # Afficher un DataFrame vide avec les colonnes
            df_vide = pd.DataFrame(columns=['Programme', 'Type_Organisateur', 'Nb_Evenements'])
            display(df_vide)
        
        # 6. Incidents par programme
        print("\n6. INCIDENTS PAR PROGRAMME")
        df_incidents = self.get_incidents_par_programme()
        if not df_incidents.empty:
            display(df_incidents)
        else:
            # Afficher un DataFrame vide avec les colonnes
            df_vide = pd.DataFrame(columns=['Programme', 'Nb_Incidents', 'Incidents_Resolus', 'Incidents_En_Cours'])
            display(df_vide)
        
        return {
            'proprietaires_locataires': df_prop_loc,
            'biens': df_biens,
            'locations': df_location,
            'evenements_periode': df_events_periode,
            'evenements_organisateur': df_events_org,
            'incidents': df_incidents
        }

for _, programme_row in df_clean.iterrows():
    programme_nom = programme_row['Programme']
    analyser_proprietaires(programme_nom, df_proprietaires)

for _, programme_row in df_clean.iterrows():
    programme_nom = programme_row['Programme']
    analyser_locataires(programme_nom, df_locataires)



for _, programme_row in df_clean.iterrows():
    programme_nom = programme_row['Programme']
    charges = programme_row.get('Charges')
    analyser_charges(programme_nom, charges)

# Extraction des charges détaillées pour tous les programmes\nprint('=== ANALYSE DÉTAILLÉE DES CHARGES DES PROPRIÉTAIRES ===\n')\n\nfor _, programme_row in df_clean.iterrows():\n    programme_nom = programme_row['Programme']\n    proprietaires = programme_row.get('Proprietaires', [])\n    \n    if proprietaires and len(proprietaires) > 0:\n        print(f'\n=== PROGRAMME: {programme_nom} ===')\n        \n        # Extraction des charges des propriétaires\n        df_charges_proprio = extraire_charges_proprietaires(proprietaires)\n        \n        if not df_charges_proprio.empty:\n            print(f'Nombre de propriétaires avec charges: {len(df_charges_proprio)}')\n            print('\nRésumé des charges par propriétaire:')\n            display(df_charges_proprio[['nom', 'prenoms', 'total_charge', 'total_payer', 'total_impayes', \n                                      'total_charge_exceptionnel', 'nb_appels_fonds', 'nb_imprevus']])\n            \n            # Statistiques globales pour ce programme\n            total_charges_programme = df_charges_proprio['total_charge'].sum()\n            total_paye_programme = df_charges_proprio['total_payer'].sum()\n            total_impaye_programme = df_charges_proprio['total_impayes'].sum()\n            \n            print(f'\nStatistiques globales du programme:')\n            print(f'- Total charges: {total_charges_programme:,.0f} XOF')\n            print(f'- Total payé: {total_paye_programme:,.0f} XOF')\n            print(f'- Total impayé: {total_impaye_programme:,.0f} XOF')\n            if total_charges_programme > 0:\n                taux_paiement = (total_paye_programme / total_charges_programme) * 100\n                print(f'- Taux de paiement: {taux_paiement:.1f}%')\n        else:\n            print('Aucune charge détaillée trouvée pour ce programme')\n    else:\n        print(f'\n=== PROGRAMME: {programme_nom} ===')\n        print('Aucun propriétaire trouvé pour ce programme')

# Extraction du détail de tous les appels de fonds\nprint('=== DÉTAIL DES APPELS DE FONDS ET CHARGES EXCEPTIONNELLES ===\n')\n\nfor _, programme_row in df_clean.iterrows():\n    programme_nom = programme_row['Programme']\n    proprietaires = programme_row.get('Proprietaires', [])\n    \n    if proprietaires and len(proprietaires) > 0:\n        print(f'\n=== PROGRAMME: {programme_nom} ===')\n        \n        # Extraction des appels de fonds détaillés\n        df_appels_fonds = extraire_appels_fonds_detailles(proprietaires)\n        \n        if not df_appels_fonds.empty:\n            print(f'Nombre total d\'appels de fonds/charges: {len(df_appels_fonds)}')\n            \n            # Affichage par type de charge\n            for type_charge in df_appels_fonds['type_charge'].unique():\n                df_type = df_appels_fonds[df_appels_fonds['type_charge'] == type_charge]\n                print(f'\n--- {type_charge} ({len(df_type)} éléments) ---')\n                display(df_type[['nom_proprietaire', 'libelle_appel', 'lot', 'ilot', 'solde', 'date_fin', 'status']])\n                \n                # Statistiques par type\n                total_type = df_type['solde'].sum()\n                print(f'Total {type_charge}: {total_type:,.0f} XOF')\n        else:\n            print('Aucun appel de fonds détaillé trouvé pour ce programme')\n    else:\n        print(f'\n=== PROGRAMME: {programme_nom} ===')\n        print('Aucun propriétaire trouvé pour ce programme')

for _, programme_row in df_clean.iterrows():
    programme_nom = programme_row['Programme']
    analyser_incidents(programme_nom, df_incidents)

for _, programme_row in df_clean.iterrows():
    programme_nom = programme_row['Programme']
    analyser_evenements(programme_nom, df_evenements)

# Création de l'instance de la classe de métriques
metrics = ESyndicMetrics(
    df_clean=df_clean,
    df_proprietaires=df_proprietaires,
    df_locataires=df_locataires,
    df_incidents=df_incidents,
    df_evenements=df_evenements
)

# Génération du rapport complet
rapport_complet = metrics.generer_rapport_complet()

# Exemple d'utilisation des métriques individuelles
print("\nEXEMPLES D'UTILISATION DES MÉTRIQUES INDIVIDUELLES")
print("="*60)

# 1. Propriétaires et locataires par programme
print("\n1. Propriétaires et locataires par programme:")
df_prop_loc = metrics.get_total_proprietaires_locataires_par_programme()
print(f"Total programmes analysés: {len(df_prop_loc)}")
if not df_prop_loc.empty:
    print(f"Programme avec le plus de personnes: {df_prop_loc.loc[df_prop_loc['Total_Personnes'].idxmax(), 'Programme']}")

# 2. Événements par période
print("\n2. Événements par période:")
df_events = metrics.get_evenements_par_programme_et_periode()
if not df_events.empty:
    total_events = df_events['Nb_Evenements'].sum()
    print(f"Total événements: {total_events}")
    if 'Annee' in df_events.columns:
        events_par_annee = df_events.groupby('Annee')['Nb_Evenements'].sum()
        print("Répartition par année:")
        for annee, nb in events_par_annee.items():
            print(f"  - {annee}: {nb} événements")
else:
    print("Aucun événement trouvé")

# 3. Incidents par programme
print("\n3. Incidents par programme:")
df_incidents_metrics = metrics.get_incidents_par_programme()
if not df_incidents_metrics.empty:
    total_incidents = df_incidents_metrics['Nb_Incidents'].sum()
    total_resolus = df_incidents_metrics['Incidents_Resolus'].sum()
    print(f"Total incidents: {total_incidents}")
    print(f"Incidents résolus: {total_resolus}")
    if total_incidents > 0:
        taux_resolution = (total_resolus / total_incidents) * 100
        print(f"Taux de résolution: {taux_resolution:.1f}%")
else:
    print("Aucun incident trouvé")

# # Sauvegarde des métriques en CSV pour réutilisation
# print("\nSAUVEGARDE DES MÉTRIQUES")
# print("="*40)
# 
# try:
#     # Sauvegarder chaque métrique
#     rapport_complet['proprietaires_locataires'].to_csv('esyndic_proprietaires_locataires.csv', index=False)
#     print("esyndic_proprietaires_locataires.csv sauvegardé")
#     
#     rapport_complet['biens'].to_csv('esyndic_biens.csv', index=False)
#     print("esyndic_biens.csv sauvegardé")
#     
#     if not rapport_complet['locations'].empty:
#         rapport_complet['locations'].to_csv('esyndic_locations.csv', index=False)
#         print("esyndic_locations.csv sauvegardé")
#     
#     if not rapport_complet['evenements_periode'].empty:
#         rapport_complet['evenements_periode'].to_csv('esyndic_evenements_periode.csv', index=False)
#         print("esyndic_evenements_periode.csv sauvegardé")
#     
#     if not rapport_complet['evenements_organisateur'].empty:
#         rapport_complet['evenements_organisateur'].to_csv('esyndic_evenements_organisateur.csv', index=False)
#         print("esyndic_evenements_organisateur.csv sauvegardé")
#     
#     if not rapport_complet['incidents'].empty:
#         rapport_complet['incidents'].to_csv('esyndic_incidents.csv', index=False)
#         print("esyndic_incidents.csv sauvegardé")
#     
#     print("\nToutes les métriques ont été sauvegardées avec succès !")
#     
# except Exception as e:
#     print(f"Erreur lors de la sauvegarde: {str(e)}")

print("Section de sauvegarde mise en commentaire - Toutes les métriques sont affichées sous forme de DataFrames")

